"""
Multi-agent interview system entry point.
Coordinates multiple specialized agents for different interview phases.
"""

import logging
import time
import async<PERSON>
from typing import Op<PERSON>
from livekit.agents import Agent, AgentSession
from livekit.plugins import google, silero
from .interview_agents import InterviewContext, IntroductionAgent, TranscriptCollector

logger = logging.getLogger("multi-agent-interviewer")


class MultiAgentInterviewer:
    """
    Main coordinator for the multi-agent interview system.
    Manages the flow between different specialized agents.
    """
    
    def __init__(self, role: str, level: str, name: str = "",
                 topics_to_cover: str = "", interviewer_questions: str = "",
                 company_info_available: str = "", llm_provider: str = "google",
                 interview_round: str = "Technical"):
        """
        Initialize the multi-agent interview system.

        Args:
            role: The job role being interviewed for
            level: The experience level (Junior, Mid-level, Senior, etc.)
            name: The candidate's name for personalization
            topics_to_cover: Specific topics to explore (newline separated)
            interviewer_questions: Specific questions to ask (newline separated)
            company_info_available: Job description content for targeted questioning
            llm_provider: LLM provider to use ("google" or "openai")
            interview_round: The interview round type (HR, Technical, Managerial, Architect, etc.)
        """
        self.context = InterviewContext(
            role=role,
            level=level,
            name=name,
            topics_to_cover=topics_to_cover,
            interviewer_questions=interviewer_questions,
            company_info_available=company_info_available,
            llm_provider=llm_provider,
            interview_round=interview_round
        )
        
        # Interview timing control
        self.interview_start_time = None
        self.target_duration_minutes = 30
        self.minimum_duration_minutes = 25
        self.timing_reminders_sent = set()
        self.timing_monitor_task = None

        # Phase timing control
        self.current_phase_start_time = None
        self.phase_durations = {
            "introduction": 3,  # 3 minutes
            "topics": 5,        # 5 minutes
            "job_relevant": 3,  # 3 minutes
            "interviewer_questions": 3,  # 3 minutes
            "candidate_questions": 5     # 5 minutes
        }
        self.phase_transition_task = None
        
        # Silence monitoring
        self.last_activity_time = None
        self.last_silence_warning_time = None
        self.silence_warnings_sent = set()
        self.silence_monitor_task = None
        
        self.is_initialized = False
        
        # Set reference to self in context so agents can trigger transitions
        self.context.multi_agent_interviewer = self

        # Set global reference for the shared transition tool
        from .interview_agents import set_current_multi_agent_interviewer
        set_current_multi_agent_interviewer(self)

        # Audio quality tracking
        self.minimum_speech_duration = 1.0  # Minimum 1 second of speech
        self.last_meaningful_input = None

        logger.info(f"MultiAgentInterviewer initialized for {role} ({level}) - Candidate: {name or 'Anonymous'}")

    async def trigger_phase_transition(self, reason: str = "Manual transition"):
        """Manually trigger a phase transition."""
        logger.info(f"Manual phase transition triggered: {reason}")
        await self.transition_to_next_phase()
    
    def get_initial_agent(self) -> Agent:
        """Get the first agent to start the interview."""
        self.current_phase_start_time = time.time()
        return IntroductionAgent(self.context)
    
    def set_session(self, session: AgentSession):
        """Set the session reference for all agents."""
        self.context.session = session
        
        # Set up transcript collection for the session
        self._setup_transcript_collection(session)

        # Set up automatic transcript saving when interview ends
        self.setup_interview_end_handler()
    
    def set_room(self, room):
        """Set the room reference for all agents."""
        self.context.room = room
    
    def _setup_transcript_collection(self, session: AgentSession):
        """Set up automatic transcript collection from session events."""
        
        @session.on("user_input_transcribed")
        def on_user_transcribed(event):
            """Capture user speech in transcript with quality filtering."""
            if event.is_final and event.transcript.strip():
                text = event.transcript.strip()
                
                self.context.transcript_collector.add_entry("Candidate", text)
                self.last_meaningful_input = time.time()
                logger.debug(f"User transcript captured: {text[:50]}...")
                
        
        @session.on("agent_speech_committed")
        def on_agent_speech(event):
            """Capture agent speech in transcript."""
            if hasattr(event, 'text') and event.text.strip():
                self.context.transcript_collector.add_entry("Interviewer", event.text)
                logger.debug(f"Agent speech captured: {event.text[:50]}...")
        
        # Alternative event handlers for different LiveKit versions
        @session.on("conversation_item_added")
        def on_conversation_item(event):
            """Capture conversation items in transcript."""
            if hasattr(event, 'item') and hasattr(event.item, 'text_content'):
                speaker = "Interviewer" if event.item.role == "assistant" else "Candidate"
                if event.item.text_content.strip():
                    self.context.transcript_collector.add_entry(speaker, event.item.text_content)
                    logger.debug(f"Conversation item captured: {speaker}: {event.item.text_content[:50]}...")
        
        logger.info("Transcript collection event handlers set up")
    
    def start_interview_timer(self):
        """Start the interview timer."""
        self.interview_start_time = time.time()
        self.last_activity_time = time.time()
        self.last_silence_warning_time = time.time()
        logger.info(f"Interview timer started - target duration: {self.target_duration_minutes} minutes")
    
    def update_user_speech_time(self):
        """Update the last time user spoke."""
        self.last_activity_time = time.time()
        self.silence_warnings_sent.clear()
        logger.info(f"🎤 User speech detected - activity time updated to {self.last_activity_time}, silence warnings reset")
    
    def update_agent_activity_time(self):
        """Update the last time agent spoke or was active."""
        self.last_activity_time = time.time()
        self.silence_warnings_sent.clear()
        logger.info(f"🤖 Agent speech detected - activity time updated to {self.last_activity_time}, silence warnings reset")
    
    async def check_silence_and_respond(self):
        """Check for extended silence and proactively respond."""
        if self.last_activity_time is None or self.context.session is None:
            return

        current_time = time.time()
        silence_duration = current_time - self.last_activity_time
        time_since_last_warning = float('inf') if self.last_silence_warning_time is None else current_time - self.last_silence_warning_time

        # Debug logging every 30 seconds to track silence duration
        if silence_duration >= 30 and int(silence_duration) % 30 == 0:
            logger.info(f"🔇 Silence duration: {silence_duration:.1f}s (last warning: {time_since_last_warning:.1f}s ago)")

        # Trigger after 60 seconds of silence, and only send warning once every 60 seconds
        if silence_duration >= 60 and time_since_last_warning >= 60:
            logger.warning(f"🚨 INTEGRITY ALERT: Complete silence detected ({silence_duration:.1f}s)")
            self.last_silence_warning_time = current_time

            try:
                silence_instruction = "I notice we've had some silence. Are you still there? Please continue with your response."

                if self.context.session and hasattr(self.context.session, 'generate_reply'):
                    await self.context.session.generate_reply(instructions=silence_instruction)
                    logger.info(f"✅ Sent silence warning after {silence_duration:.1f}s of complete silence")
                else:
                    logger.warning(f"🚨 SILENCE WARNING (could not send): Complete silence for {silence_duration:.1f}s")

            except Exception as e:
                logger.error(f"Failed to send silence warning: {e}")
    
    async def start_silence_monitoring(self):
        """Start the background silence monitoring task."""
        if self.silence_monitor_task is not None:
            return
        
        async def monitor_silence():
            logger.info("Silence monitoring started")
            check_count = 0
            while True:
                try:
                    check_count += 1
                    if check_count % 15 == 0:
                        logger.info(f"Silence monitoring active - check #{check_count}")
                    await self.check_silence_and_respond()
                    await asyncio.sleep(2)
                except asyncio.CancelledError:
                    logger.info("Silence monitoring cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in silence monitoring: {e}")
                    await asyncio.sleep(5)
        
        self.silence_monitor_task = asyncio.create_task(monitor_silence())
    
    def stop_silence_monitoring(self):
        """Stop the silence monitoring task."""
        if self.silence_monitor_task is not None:
            self.silence_monitor_task.cancel()
            self.silence_monitor_task = None
            logger.info("Silence monitoring stopped")
    
    async def check_timing_and_enforce(self):
        """Check interview timing and proactively enforce minimum duration."""
        if self.interview_start_time is None or self.context.session is None:
            return
        
        elapsed_minutes = self.get_interview_elapsed_minutes()
        
       
        
       
    
    async def start_timing_monitoring(self):
        """Start the background timing enforcement task."""
        if self.timing_monitor_task is not None:
            return
        
        async def monitor_timing():
            logger.info("Timing enforcement monitoring started")
            check_count = 0
            while True:
                try:
                    check_count += 1
                    if check_count % 30 == 0:
                        elapsed = self.get_interview_elapsed_minutes()
                        logger.info(f"Timing monitoring active - {elapsed:.1f} minutes elapsed")
                    await self.check_timing_and_enforce()
                    await asyncio.sleep(2)
                except asyncio.CancelledError:
                    logger.info("Timing monitoring cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in timing monitoring: {e}")
                    await asyncio.sleep(5)
        
        self.timing_monitor_task = asyncio.create_task(monitor_timing())

    async def start_phase_transition_monitoring(self):
        """Start the background phase transition monitoring task."""
        if self.phase_transition_task is not None:
            return

        async def monitor_phase_transitions():
            logger.info("Phase transition monitoring started")
            while True:
                try:
                    await self.check_and_handle_phase_transition()
                    await asyncio.sleep(10)  # Check every 10 seconds for faster transitions
                except asyncio.CancelledError:
                    logger.info("Phase transition monitoring cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in phase transition monitoring: {e}")
                    await asyncio.sleep(5)

        self.phase_transition_task = asyncio.create_task(monitor_phase_transitions())

    async def check_and_handle_phase_transition(self):
        """Check if it's time to transition to the next phase and handle it."""
        if self.current_phase_start_time is None or self.context.session is None:
            logger.debug("Phase transition check skipped - no start time or session")
            return

        current_time = time.time()
        phase_elapsed_minutes = (current_time - self.current_phase_start_time) / 60.0
        current_phase = self.context.current_phase

        logger.debug(f"Phase check: '{current_phase}' running for {phase_elapsed_minutes:.1f} minutes")

        # Check if current phase has exceeded its duration
        if current_phase in self.phase_durations:
            phase_duration = self.phase_durations[current_phase]

            if phase_elapsed_minutes >= phase_duration:
                logger.info(f"🔄 Phase '{current_phase}' has run for {phase_elapsed_minutes:.1f} minutes (limit: {phase_duration}), transitioning...")
                # await self.transition_to_next_phase()
            else:
                logger.debug(f"Phase '{current_phase}': {phase_elapsed_minutes:.1f}/{phase_duration} minutes")

    async def transition_to_next_phase(self):
        """Transition to the next interview phase."""
        try:
            # Check if context and session are available
            if self.context is None:
                logger.error("❌ No context available for transition")
                return

            if self.context.session is None:
                logger.error("❌ No session available for transition")
                return

            current_phase = self.context.current_phase
            logger.info(f"🔄 Starting transition from '{current_phase}' phase")

            self.context.mark_phase_complete(current_phase)

            # Determine next phase
            next_phase = self.context.get_next_phase()
            if not next_phase:
                logger.info("✅ All phases complete, staying in current phase")
                return

            logger.info(f"🎯 Next phase determined: '{next_phase}'")
            logger.info(f"🔍 Session type: {type(self.context.session)}")
            logger.info(f"🔍 Session available methods: {[method for method in dir(self.context.session) if 'update' in method.lower()]}")
            self.context.current_phase = next_phase
            self.current_phase_start_time = time.time()

            # Create the appropriate next agent
            next_agent = None
            if next_phase == "topics":
                from .interview_agents import TopicsAgent
                next_agent = TopicsAgent(self.context)
                logger.info("📝 Created TopicsAgent")
            elif next_phase == "job_relevant":
                from .interview_agents import JobRelevantAgent
                logger.info("💼 Creating JobRelevantAgent...")
                next_agent = JobRelevantAgent(self.context)
                logger.info("💼 JobRelevantAgent created successfully")
                logger.info(f"💼 Agent type: {type(next_agent).__name__}")
                logger.info(f"💼 Agent has instructions: {hasattr(next_agent, '_instructions')}")
            elif next_phase == "interviewer_questions":
                from .interview_agents import InterviewerQuestionsAgent
                next_agent = InterviewerQuestionsAgent(self.context)
                logger.info("❓ Created InterviewerQuestionsAgent")
            elif next_phase == "candidate_questions":
                from .interview_agents import ClosingAgent
                next_agent = ClosingAgent(self.context)
                logger.info("🏁 Created ClosingAgent")
            else:
                logger.error(f"❌ Unknown next phase: {next_phase}")
                return

            if next_agent is None:
                logger.error("❌ Failed to create next agent")
                return

            # Check if session is available
            if self.context.session is None:
                logger.error("❌ No session available for agent transition")
                return

            # Update the session with the new agent
            logger.info(f"🔄 Updating session with new agent...")
            logger.info(f"🔍 Available session methods: {[method for method in dir(self.context.session) if not method.startswith('_')]}")

            try:
                # Try multiple methods to update the agent
                success = False

                # Method 1: Try update_agent
                if hasattr(self.context.session, 'update_agent'):
                    logger.info("🔄 Trying update_agent method...")
                    update_result = await self.context.session.update_agent(next_agent)
                    logger.info(f"🔄 update_agent result: {update_result}")
                    success = True

                # Method 2: Try setting the agent directly
                elif hasattr(self.context.session, 'agent'):
                    logger.info("🔄 Trying direct agent assignment...")
                    self.context.session.agent = next_agent
                    logger.info("🔄 Direct agent assignment completed")
                    success = True

                # Method 3: Try using set_agent if available
                elif hasattr(self.context.session, 'set_agent'):
                    logger.info("🔄 Trying set_agent method...")
                    await self.context.session.set_agent(next_agent)
                    logger.info("🔄 set_agent completed")
                    success = True

                # Method 4: Try updating the current agent's instructions instead
                else:
                    logger.info("🔄 Trying to update current agent instructions...")
                    if hasattr(self.context.session, '_agent') and hasattr(self.context.session._agent, '_instructions'):
                        # Update the current agent's instructions to match the new phase
                        self.context.session._agent._instructions = next_agent._instructions
                        logger.info("🔄 Agent instructions updated")
                        success = True
                    else:
                        logger.error("❌ No known method to update agent found")
                        logger.error(f"❌ Session type: {type(self.context.session)}")
                        logger.error(f"❌ Session attributes: {[attr for attr in dir(self.context.session) if 'agent' in attr.lower()]}")

                        # Last resort: Just log the transition and continue with automatic introduction
                        logger.warning("⚠️ Proceeding with phase transition without agent update")
                        success = True

                if success:
                    logger.info(f"✅ Successfully transitioned to '{next_phase}' phase with {type(next_agent).__name__}")

                    # Wait longer for the agent to be fully updated and session to sync
                    logger.info("⏳ Waiting for agent and session to sync...")
                    await asyncio.sleep(2)

                    # Generate an automatic introduction for the new phase
                    logger.info(f"🎤 About to generate phase introduction for '{next_phase}'")
                    await self._generate_phase_introduction(next_phase)

                    # Also try to update the agent's behavior by sending new instructions
                    try:
                        phase_instructions = self._get_phase_instructions(next_phase)
                        logger.info(f"🔄 Sending new phase instructions to agent...")
                        await self.context.session.generate_reply(
                            instructions=f"You are now in the {next_phase} phase. {phase_instructions}"
                        )
                    except Exception as instr_error:
                        logger.warning(f"⚠️ Could not send phase instructions: {instr_error}")
                else:
                    logger.error("❌ Failed to update agent - no method succeeded")

            except TypeError as type_error:
                logger.error(f"❌ TypeError during session update (likely None object): {type_error}")
                logger.error(f"❌ Session object: {self.context.session}")
                logger.error(f"❌ Next agent: {next_agent}")
                return

            except Exception as update_error:
                logger.error(f"❌ Failed to update session with new agent: {update_error}")
                logger.error(f"❌ Error type: {type(update_error)}")
                return

        except Exception as e:
            logger.error(f"❌ Error during phase transition: {e}", exc_info=True)

    async def _generate_phase_introduction(self, phase: str):
        """Generate an automatic introduction when transitioning to a new phase."""
        try:
            # Get recent context for more natural transitions
            recent_context = self.context.transcript_collector.get_recent_context()

            phase_introductions = {
                "topics": f"Thank you for that introduction! Now I'd like to dive deeper into your technical experience. Based on what you've shared, let's explore some specific areas in more detail.",
                "job_relevant": f"Excellent! Now I'd like to shift focus to questions specifically related to this {self.context.role} position and what we're looking for in this role.",
                "interviewer_questions": f"Perfect! I have some specific questions that our hiring team wanted me to ask you about your experience and approach.",
                "candidate_questions": f"Great! We've covered a lot of ground in this interview. Now I'd love to turn the tables - what questions do you have about the role, the company, our team, or anything else?"
            }

            # Add flinkk AI identity reminder to the introduction
            introduction = phase_introductions.get(phase, f"Let's move on to the next part of our interview.")
            introduction_with_identity = f"Remember, you are Flinkk AI. {introduction}"

            logger.info(f"🎤 Generating automatic introduction for '{phase}' phase")

            # Small delay to make transition feel natural
            await asyncio.sleep(1)

            # Debug session state
            logger.info(f"🔍 Session type: {type(self.context.session)}")
            logger.info(f"🔍 Session has agent: {hasattr(self.context.session, 'agent')}")
            if hasattr(self.context.session, 'agent'):
                logger.info(f"🔍 Current agent type: {type(self.context.session.agent)}")

            # Make the agent automatically speak the phase introduction using generate_reply
            logger.info(f"🎤 Attempting to generate phase introduction: '{introduction}'")

            # Try multiple approaches with generate_reply (the correct method for real-time models)
            success = False

            # Method 1: Direct instructions
            try:
                logger.info("🎤 Method 1: generate_reply with direct instructions")
                await self.context.session.generate_reply(instructions=introduction)
                logger.info(f"✅ Phase introduction generated successfully")
                success = True
            except Exception as e1:
                logger.warning(f"⚠️ Method 1 failed: {e1}")

            # Method 2: Prompt the agent to start the new phase
            if not success:
                try:
                    logger.info("🎤 Method 2: generate_reply with phase start prompt")
                    prompt = f"You are now starting the {phase} phase of the interview. Begin by saying: {introduction}"
                    await self.context.session.generate_reply(instructions=prompt)
                    logger.info(f"✅ Phase introduction generated with phase prompt")
                    success = True
                except Exception as e2:
                    logger.warning(f"⚠️ Method 2 failed: {e2}")

            # Method 3: Simple trigger without specific instructions
            if not success:
                try:
                    logger.info("🎤 Method 3: generate_reply without instructions (let agent decide)")
                    await self.context.session.generate_reply()
                    logger.info(f"✅ Phase introduction generated without instructions")
                    success = True
                except Exception as e3:
                    logger.warning(f"⚠️ Method 3 failed: {e3}")

            # Method 4: Force agent to speak with explicit command
            if not success:
                try:
                    logger.info("🎤 Method 4: generate_reply with explicit speak command")
                    await self.context.session.generate_reply(instructions="Start speaking now to begin this phase of the interview")
                    logger.info(f"✅ Phase introduction generated with speak command")
                    success = True
                except Exception as e4:
                    logger.error(f"❌ Method 4 failed: {e4}")

            if not success:
                logger.error("❌ All methods to generate phase introduction failed - agent may not speak automatically")

        except Exception as e:
            logger.error(f"❌ Error generating phase introduction: {e}", exc_info=True)

    def _get_phase_instructions(self, phase: str) -> str:
        """Get specific instructions for each phase."""
        instructions = {
            "topics": f"You are now conducting the TOPICS EXPLORATION phase. Ask about specific technical topics: {', '.join(self.context.parsed_topics) if self.context.parsed_topics else 'general technical questions'}. Keep questions concise and wait for meaningful responses.",
            "job_relevant": f"You are now conducting the JOB-RELEVANT QUESTIONS phase. Ask about experience with technologies and skills mentioned in the job requirements. Keep questions concise and wait for meaningful responses.",
            "interviewer_questions": f"You are now conducting the INTERVIEWER QUESTIONS phase. Ask these specific questions: {'; '.join(self.context.parsed_interviewer_questions) if self.context.parsed_interviewer_questions else 'general interview questions'}. Keep questions concise and wait for meaningful responses.",
            "candidate_questions": "You are now conducting the CLOSING phase. Ask if the candidate has questions about the role, company, or team. Answer their questions helpfully."
        }
        return instructions.get(phase, f"Continue with the {phase} phase of the interview.")

    async def complete_interview(self):
        """Complete the interview and save transcript."""
        try:
            logger.info("🏁 Completing interview and saving transcript...")

            # Save the complete transcript
            full_transcript = self.context.transcript_collector.get_full_transcript()

            room_name = ""
            if self.context.room:
                room_name = self.context.room.name

            


        except Exception as e:
            logger.error(f"❌ Error completing interview: {e}", exc_info=True)

    def setup_interview_end_handler(self):
        """Set up handler for when interview ends to automatically save transcript."""
        if self.context.session:
            # Try multiple event names for session ending
            try:
                @self.context.session.on("session_ended")
                def on_session_ended(event):
                    """Automatically save transcript when session ends."""
                    logger.info("🏁 Session ended - automatically saving transcript...")
                    asyncio.create_task(self._save_transcript_on_end())
            except Exception as e:
                logger.warning(f"⚠️ Could not register session_ended handler: {e}")

            # Also try to listen for room disconnect events
            if self.context.room:
                try:
                    @self.context.room.on("disconnected")
                    def on_room_disconnected(event):
                        """Save transcript when room disconnects."""
                        logger.info("🏁 Room disconnected - automatically saving transcript...")
                        asyncio.create_task(self._save_transcript_on_end())
                except Exception as e:
                    logger.warning(f"⚠️ Could not register room disconnected handler: {e}")

    async def _save_transcript_on_end(self):
        """Helper method to save transcript when session ends."""
        try:
            await self.complete_interview()
        except Exception as e:
            logger.error(f"❌ Error saving transcript on session end: {e}", exc_info=True)

    def stop_phase_transition_monitoring(self):
        """Stop the phase transition monitoring task."""
        if self.phase_transition_task is not None:
            self.phase_transition_task.cancel()
            self.phase_transition_task = None
            logger.info("Phase transition monitoring stopped")
    
    def stop_timing_monitoring(self):
        """Stop the timing monitoring task."""
        if self.timing_monitor_task is not None:
            self.timing_monitor_task.cancel()
            self.timing_monitor_task = None
            logger.info("Timing monitoring stopped")
    
    def get_interview_elapsed_minutes(self) -> float:
        """Get elapsed interview time in minutes."""
        if self.interview_start_time is None:
            return 0.0
        return (time.time() - self.interview_start_time) / 60.0
    
    def get_agent_info(self) -> dict:
        """Get agent configuration information."""
        return {
            "role": self.context.role,
            "level": self.context.level,
            "name": self.context.name,
            "current_phase": self.context.current_phase,
            "completed_phases": list(self.context.completed_phases),
            "is_initialized": self.is_initialized,
            "agent_type": "MultiAgentInterviewer",
            "interview_elapsed_minutes": self.get_interview_elapsed_minutes(),
            "target_duration_minutes": self.target_duration_minutes,
            "transcript_entries": len(self.context.transcript_collector.transcript_entries)
        }
